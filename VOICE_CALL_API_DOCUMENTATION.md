# AI Voice Mate - Voice Call API Documentation

## Overview

This documentation provides comprehensive guidance for connecting voice calls to the AI agent application. The system uses WebSocket connections to handle real-time voice communication between clients and AI tutoring agents.

## Server Configuration

The WebSocket server runs on:
- **Host**: `0.0.0.0`
- **Port**: `5010`
- **Protocol**: WebSocket (ws://)

## Connection URL
```
ws://localhost:5010
```

## Message Protocol

All messages are sent as JSON objects with the following structure:
```json
{
  "type": "message_type",
  "session": "unique_session_id",
  "data": "message_data"
}
```

## Message Types Reference

### 1. User Registration
**Type**: `store_user`
```json
{
  "type": "store_user",
  "session": "user_session_123",
  "data": {
    "name": "<PERSON> Doe",
    "mobile": "+1234567890",
    "userId": "user_001",
    "sentences": [],
    "sessionType": "call",
    "target": "english_tutor"
  }
}
```

### 2. Start AI Call
**Type**: `start_ai_call`
```json
{
  "type": "start_ai_call",
  "session": "user_session_123",
  "data": null
}
```

### 3. Audio Streaming
**Type**: `audio_chunk`
```json
{
  "type": "audio_chunk",
  "session": "user_session_123",
  "data": [255, 128, 64, 32, ...] // Array of audio bytes
}
```

### 4. Enable AI Listening
**Type**: `ai_start_listening`
```json
{
  "type": "ai_start_listening",
  "session": "user_session_123",
  "data": null
}
```

### 5. Voice Action Stop
**Type**: `voice_action_stop`
```json
{
  "type": "voice_action_stop",
  "session": "user_session_123",
  "data": "stop_recording"
}
```

### 6. Text Input
**Type**: `text_input`
```json
{
  "type": "text_input",
  "session": "user_session_123",
  "data": "Hello, I want to practice English"
}
```

### 7. End AI Call
**Type**: `end_ai_call`
```json
{
  "type": "end_ai_call",
  "session": "user_session_123",
  "data": null
}
```

### 8. Request Active Users
**Type**: `request_active_users`
```json
{
  "type": "request_active_users",
  "session": "user_session_123",
  "data": null
}
```

## Server Response Messages

### User Registration Response
```json
{
  "type": "store_user",
  "data": "user added successfully"
}
```

### AI Response
```json
{
  "type": "ai_response",
  "data": "Hello! I'm your AI tutor. How can I help you today?"
}
```

### Speech to Text Response
```json
{
  "type": "speech_text",
  "data": "Hello, I want to practice English"
}
```

### Active Users Response
```json
{
  "type": "active_users",
  "data": [
    {
      "session": "user_session_123",
      "name": "John Doe",
      "mobile": "+1234567890",
      "user_id": "user_001",
      "sentences": []
    }
  ]
}
```

### Error Response
```json
{
  "type": "error",
  "message": "Error description"
}
```

### End Call Notification
```json
{
  "type": "ai_end_call",
  "data": "ai call completed"
}
```

### AI Audio Response (Audio Chunks)
**Type**: `llm_answer`
```json
{
  "type": "llm_answer",
  "data": "UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBjOOzfPZeSsFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBjOOzfPZeSsFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwS=="
}
```

### Transcript Batch (Real-time transcription)
**Type**: `transcript_batch`
```json
{
  "type": "transcript_batch",
  "data": "Hello, I want to practice English"
}
```

### Pause Audio Recording
**Type**: `pause_audio_recording`
```json
{
  "type": "pause_audio_recording",
  "data": ""
}
```

## Client Implementation Examples

### JavaScript WebSocket Client

```javascript
class VoiceCallClient {
  constructor(serverUrl = 'ws://localhost:5010') {
    this.serverUrl = serverUrl;
    this.websocket = null;
    this.sessionId = this.generateSessionId();
    this.mediaRecorder = null;
    this.audioStream = null;
    this.audioContext = null;
    this.audioChunks = [];
    this.isPlaying = false;
  }

  generateSessionId() {
    return 'session_' + Math.random().toString(36).substr(2, 9);
  }

  async connect() {
    return new Promise((resolve, reject) => {
      this.websocket = new WebSocket(this.serverUrl);
      
      this.websocket.onopen = () => {
        console.log('Connected to AI Voice Mate server');
        resolve();
      };
      
      this.websocket.onmessage = (event) => {
        const message = JSON.parse(event.data);
        this.handleServerMessage(message);
      };
      
      this.websocket.onerror = (error) => {
        console.error('WebSocket error:', error);
        reject(error);
      };
      
      this.websocket.onclose = () => {
        console.log('Disconnected from server');
      };
    });
  }

  handleServerMessage(message) {
    switch (message.type) {
      case 'ai_response':
        console.log('AI Response:', message.data);
        this.playTextToSpeech(message.data);
        break;
      case 'llm_answer':
        console.log('Received AI audio chunk');
        this.playAudioChunk(message.data);
        break;
      case 'speech_text':
        console.log('Transcribed text:', message.data);
        break;
      case 'transcript_batch':
        console.log('Real-time transcript:', message.data);
        this.displayTranscript(message.data);
        break;
      case 'pause_audio_recording':
        console.log('Server requested to pause audio recording');
        this.pauseRecording();
        break;
      case 'ai_end_call':
        console.log('Call ended by AI');
        this.disconnect();
        break;
      case 'error':
        console.error('Server error:', message.message);
        break;
    }
  }

  registerUser(userData) {
    const message = {
      type: 'store_user',
      session: this.sessionId,
      data: {
        name: userData.name,
        mobile: userData.mobile,
        userId: userData.userId,
        sentences: [],
        sessionType: 'call',
        target: 'english_tutor',
        ...userData
      }
    };
    this.sendMessage(message);
  }

  async startVoiceCall() {
    // Start AI call
    this.sendMessage({
      type: 'start_ai_call',
      session: this.sessionId,
      data: null
    });

    // Enable AI listening
    this.sendMessage({
      type: 'ai_start_listening',
      session: this.sessionId,
      data: null
    });

    // Start recording audio
    await this.startAudioRecording();
  }

  async startAudioRecording() {
    try {
      this.audioStream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          sampleRate: 16000,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true
        } 
      });

      this.mediaRecorder = new MediaRecorder(this.audioStream, {
        mimeType: 'audio/webm;codecs=opus',
        bitsPerSecond: 16000
      });

      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.sendAudioChunk(event.data);
        }
      };

      this.mediaRecorder.start(100); // Send chunks every 100ms
    } catch (error) {
      console.error('Error starting audio recording:', error);
    }
  }

  async sendAudioChunk(audioData) {
    const arrayBuffer = await audioData.arrayBuffer();
    const uint8Array = new Uint8Array(arrayBuffer);
    const audioBytes = Array.from(uint8Array);

    const message = {
      type: 'audio_chunk',
      session: this.sessionId,
      data: audioBytes
    };
    this.sendMessage(message);
  }

  stopVoiceRecording() {
    this.sendMessage({
      type: 'voice_action_stop',
      session: this.sessionId,
      data: 'stop_recording'
    });
  }

  sendTextMessage(text) {
    this.sendMessage({
      type: 'text_input',
      session: this.sessionId,
      data: text
    });
  }

  endCall() {
    this.sendMessage({
      type: 'end_ai_call',
      session: this.sessionId,
      data: null
    });
    
    if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
      this.mediaRecorder.stop();
    }
    
    if (this.audioStream) {
      this.audioStream.getTracks().forEach(track => track.stop());
    }
  }

  sendMessage(message) {
    if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
      this.websocket.send(JSON.stringify(message));
    } else {
      console.error('WebSocket is not connected');
    }
  }

  async initAudioContext() {
    if (!this.audioContext) {
      this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
    }
    if (this.audioContext.state === 'suspended') {
      await this.audioContext.resume();
    }
  }

  async playAudioChunk(base64AudioData) {
    try {
      await this.initAudioContext();
      
      // Decode base64 to binary
      const binaryString = atob(base64AudioData);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }
      
      // Decode audio data
      const audioBuffer = await this.audioContext.decodeAudioData(bytes.buffer);
      
      // Create and play audio source
      const source = this.audioContext.createBufferSource();
      source.buffer = audioBuffer;
      source.connect(this.audioContext.destination);
      source.start();
      
    } catch (error) {
      console.error('Error playing audio chunk:', error);
    }
  }

  displayTranscript(text) {
    // Update UI with real-time transcript
    console.log('Real-time transcript:', text);
    // You can update DOM elements here to show transcription
    const transcriptElement = document.getElementById('transcript');
    if (transcriptElement) {
      transcriptElement.textContent = text;
    }
  }

  pauseRecording() {
    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
      this.mediaRecorder.pause();
      console.log('Recording paused');
    }
  }

  resumeRecording() {
    if (this.mediaRecorder && this.mediaRecorder.state === 'paused') {
      this.mediaRecorder.resume();
      console.log('Recording resumed');
    }
  }

  disconnect() {
    if (this.websocket) {
      this.websocket.close();
    }
    if (this.audioContext) {
      this.audioContext.close();
    }
  }

  playTextToSpeech(text) {
    // Fallback text-to-speech for text responses
    if ('speechSynthesis' in window) {
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.rate = 0.8;
      utterance.pitch = 1;
      speechSynthesis.speak(utterance);
    }
  }
}

// Usage Example
async function initializeVoiceCall() {
  const client = new VoiceCallClient();
  
  try {
    await client.connect();
    
    // Register user
    client.registerUser({
      name: 'John Doe',
      mobile: '+1234567890',
      userId: 'user_001'
    });
    
    // Start voice call
    await client.startVoiceCall();
    
    // Send a text message
    setTimeout(() => {
      client.sendTextMessage('Hello, I want to practice English conversation');
    }, 2000);
    
    // End call after 30 seconds (for demo)
    setTimeout(() => {
      client.endCall();
    }, 30000);
    
  } catch (error) {
    console.error('Failed to initialize voice call:', error);
  }
}
```

### Python WebSocket Client

```python
import asyncio
import json
import websockets
import pyaudio
import threading
from typing import Optional

class VoiceCallClient:
    def __init__(self, server_url: str = "ws://localhost:5010"):
        self.server_url = server_url
        self.websocket: Optional[websockets.WebSocketServerProtocol] = None
        self.session_id = f"session_{id(self)}"
        self.audio_stream = None
        self.is_recording = False
        self.playback_stream = None
        self.audio_chunks = []
        
        # Audio configuration
        self.chunk_size = 1024
        self.sample_rate = 16000
        self.channels = 1
        self.audio_format = pyaudio.paInt16

    async def connect(self):
        """Connect to the WebSocket server"""
        try:
            self.websocket = await websockets.connect(self.server_url)
            print("Connected to AI Voice Mate server")
        except Exception as e:
            print(f"Failed to connect: {e}")
            raise

    async def listen_for_messages(self):
        """Listen for messages from the server"""
        try:
            async for message in self.websocket:
                data = json.loads(message)
                await self.handle_server_message(data)
        except websockets.exceptions.ConnectionClosed:
            print("Connection closed by server")
        except Exception as e:
            print(f"Error listening for messages: {e}")

    async def handle_server_message(self, message: dict):
        """Handle incoming server messages"""
        message_type = message.get("type")
        
        if message_type == "ai_response":
            print(f"AI Response: {message['data']}")
            self.play_text_to_speech(message['data'])
        elif message_type == "llm_answer":
            print("Received AI audio chunk")
            await self.play_audio_chunk(message['data'])
        elif message_type == "speech_text":
            print(f"Transcribed text: {message['data']}")
        elif message_type == "transcript_batch":
            print(f"Real-time transcript: {message['data']}")
            self.display_transcript(message['data'])
        elif message_type == "pause_audio_recording":
            print("Server requested to pause audio recording")
            self.pause_recording()
        elif message_type == "ai_end_call":
            print("Call ended by AI")
            await self.disconnect()
        elif message_type == "error":
            print(f"Server error: {message['message']}")

    async def register_user(self, user_data: dict):
        """Register user with the server"""
        message = {
            "type": "store_user",
            "session": self.session_id,
            "data": {
                "name": user_data.get("name", "Unknown"),
                "mobile": user_data.get("mobile", ""),
                "userId": user_data.get("userId", ""),
                "sentences": [],
                "sessionType": "call",
                "target": "english_tutor",
                **user_data
            }
        }
        await self.send_message(message)

    async def start_voice_call(self):
        """Start AI voice call"""
        # Start AI call
        await self.send_message({
            "type": "start_ai_call",
            "session": self.session_id,
            "data": None
        })

        # Enable AI listening
        await self.send_message({
            "type": "ai_start_listening",
            "session": self.session_id,
            "data": None
        })

        # Start audio recording in a separate thread
        self.start_audio_recording()

    def start_audio_recording(self):
        """Start recording audio in a separate thread"""
        self.is_recording = True
        audio_thread = threading.Thread(target=self._record_audio)
        audio_thread.daemon = True
        audio_thread.start()

    def _record_audio(self):
        """Record audio and send chunks to server"""
        p = pyaudio.PyAudio()
        
        try:
            stream = p.open(
                format=self.audio_format,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                frames_per_buffer=self.chunk_size
            )

            while self.is_recording:
                audio_data = stream.read(self.chunk_size)
                audio_bytes = list(audio_data)
                
                message = {
                    "type": "audio_chunk",
                    "session": self.session_id,
                    "data": audio_bytes
                }
                
                # Send audio chunk asynchronously
                asyncio.run_coroutine_threadsafe(
                    self.send_message(message), 
                    asyncio.get_event_loop()
                )

        except Exception as e:
            print(f"Error recording audio: {e}")
        finally:
            if 'stream' in locals():
                stream.stop_stream()
                stream.close()
            p.terminate()

    async def stop_voice_recording(self):
        """Stop voice recording"""
        self.is_recording = False
        await self.send_message({
            "type": "voice_action_stop",
            "session": self.session_id,
            "data": "stop_recording"
        })

    async def send_text_message(self, text: str):
        """Send text message to AI"""
        await self.send_message({
            "type": "text_input",
            "session": self.session_id,
            "data": text
        })

    async def end_call(self):
        """End the AI call"""
        self.is_recording = False
        await self.send_message({
            "type": "end_ai_call",
            "session": self.session_id,
            "data": None
        })

    async def send_message(self, message: dict):
        """Send message to server"""
        if self.websocket:
            await self.websocket.send(json.dumps(message))
        else:
            print("WebSocket is not connected")

    async def play_audio_chunk(self, base64_audio_data: str):
        """Play received audio chunk from server"""
        try:
            import base64
            import io
            import wave
            
            # Decode base64 audio data
            audio_bytes = base64.b64decode(base64_audio_data)
            
            # Create a BytesIO object to read audio data
            audio_buffer = io.BytesIO(audio_bytes)
            
            # Try to play audio using pyaudio
            p = pyaudio.PyAudio()
            
            try:
                # For WAV format, try to read with wave module
                with wave.open(audio_buffer, 'rb') as wf:
                    # Get audio properties
                    sample_rate = wf.getframerate()
                    channels = wf.getnchannels()
                    sample_width = wf.getsampwidth()
                    
                    # Convert sample width to PyAudio format
                    if sample_width == 1:
                        audio_format = pyaudio.paUInt8
                    elif sample_width == 2:
                        audio_format = pyaudio.paInt16
                    elif sample_width == 4:
                        audio_format = pyaudio.paInt32
                    else:
                        audio_format = pyaudio.paFloat32
                    
                    # Open playback stream
                    stream = p.open(
                        format=audio_format,
                        channels=channels,
                        rate=sample_rate,
                        output=True
                    )
                    
                    # Play audio
                    data = wf.readframes(1024)
                    while data:
                        stream.write(data)
                        data = wf.readframes(1024)
                    
                    stream.stop_stream()
                    stream.close()
                    
            except wave.Error:
                # If not WAV format, try to play as raw audio
                stream = p.open(
                    format=self.audio_format,
                    channels=self.channels,
                    rate=self.sample_rate,
                    output=True
                )
                stream.write(audio_bytes)
                stream.stop_stream()
                stream.close()
                
            finally:
                p.terminate()
                
        except Exception as e:
            print(f"Error playing audio chunk: {e}")

    def display_transcript(self, text: str):
        """Display real-time transcript"""
        print(f"Real-time transcript: {text}")
        # You can implement UI updates here if using a GUI framework

    def pause_recording(self):
        """Pause audio recording"""
        if self.is_recording:
            self.is_recording = False
            print("Recording paused by server request")

    def resume_recording(self):
        """Resume audio recording"""
        if not self.is_recording:
            self.start_audio_recording()
            print("Recording resumed")

    def play_text_to_speech(self, text: str):
        """Play text using text-to-speech (fallback)"""
        try:
            import pyttsx3
            engine = pyttsx3.init()
            engine.say(text)
            engine.runAndWait()
        except ImportError:
            print(f"Text-to-speech not available. Text: {text}")

    async def disconnect(self):
        """Disconnect from server"""
        self.is_recording = False
        if self.websocket:
            await self.websocket.close()

# Usage Example
async def main():
    client = VoiceCallClient()
    
    try:
        await client.connect()
        
        # Register user
        await client.register_user({
            "name": "John Doe",
            "mobile": "+1234567890",
            "userId": "user_001"
        })
        
        # Start listening for messages
        listen_task = asyncio.create_task(client.listen_for_messages())
        
        # Start voice call
        await client.start_voice_call()
        
        # Send a text message after 2 seconds
        await asyncio.sleep(2)
        await client.send_text_message("Hello, I want to practice English conversation")
        
        # Keep the connection alive for 30 seconds
        await asyncio.sleep(30)
        
        # End call
        await client.end_call()
        
        # Cancel listening task
        listen_task.cancel()
        
    except Exception as e:
        print(f"Error in main: {e}")
    finally:
        await client.disconnect()

if __name__ == "__main__":
    asyncio.run(main())
```

## Voice Call Workflow

### Complete Voice Call Session Flow

1. **Connection & Registration**
   ```
   Client -> Server: WebSocket Connection
   Client -> Server: store_user message
   Server -> Client: "user added successfully"
   ```

2. **Start Voice Call**
   ```
   Client -> Server: start_ai_call
   Client -> Server: ai_start_listening
   Client -> Server: Continuous audio_chunk messages
   ```

3. **AI Processing & Response**
   ```
   Server -> Client: transcript_batch (real-time transcription)
   Server -> Client: pause_audio_recording (pause user recording)
   Server -> Client: llm_answer (AI audio chunks - base64 encoded)
   Server -> Client: speech_text (final transcribed user speech)
   Server -> Client: ai_response (AI text response - fallback)
   ```

4. **Ongoing Conversation**
   ```
   Client -> Server: audio_chunk (user speaking)
   Server -> Client: transcript_batch (real-time transcription)
   Client -> Server: voice_action_stop (user stops speaking)
   Server -> Client: pause_audio_recording (pause recording)
   Server -> Client: llm_answer (AI audio response chunks)
   ```

5. **End Call**
   ```
   Client -> Server: end_ai_call
   Server -> Client: ai_end_call
   Connection closed
   ```

### Session Types

- **chat**: Text-based conversation (600s disconnect delay)
- **call**: Voice-based conversation (5s disconnect delay)

## Error Handling

### Common Error Responses

1. **JSON Decode Error**
   ```json
   {"type": "error", "message": "Invalid JSON format"}
   ```

2. **Missing Key Error**
   ```json
   {"type": "error", "message": "Missing key in data"}
   ```

3. **User Not Found**
   - Server logs warning for missing user session
   - No response sent to client

4. **Connection Closed**
   - Automatic cleanup after disconnect delay
   - Audio files saved before cleanup

## Audio Requirements

### Supported Audio Formats
- **Sample Rate**: 16kHz recommended
- **Channels**: Mono (1 channel)
- **Format**: PCM 16-bit or WebM with Opus codec
- **Chunk Size**: 1024 bytes recommended

### Audio Processing Pipeline

#### Client to Server (Speech Input)
1. Client captures microphone input
2. Audio converted to byte array
3. Sent as `audio_chunk` messages
4. Server processes with speech-to-text
5. Server sends `transcript_batch` for real-time feedback

#### Server to Client (AI Response)
1. AI generates text response
2. Server converts text to speech using TTS engine
3. Audio split into 512-byte chunks
4. Each chunk encoded as base64
5. Sent as `llm_answer` messages
6. Client decodes and plays audio chunks sequentially

#### Audio Chunk Format
- **Encoding**: Base64 string
- **Format**: WAV audio data
- **Chunk Size**: 512 bytes per message
- **Sample Rate**: Varies (typically 16kHz or 22kHz)
- **Channels**: Mono (1 channel)

## Best Practices

### Connection Management
- Always register user before starting voice call
- Use unique session IDs for each connection
- Handle connection drops gracefully
- Implement automatic reconnection logic

### Audio Streaming
- Send audio chunks frequently (every 100ms)
- Stop recording when user stops speaking
- Handle microphone permissions properly
- Implement noise cancellation when possible

### Error Recovery
- Retry connections on failure
- Validate message format before sending
- Handle server errors gracefully
- Provide user feedback for issues

### Performance Optimization
- Use WebSocket compression when available
- Minimize audio chunk size for low latency
- Implement audio buffering for smooth playback
- Monitor connection quality

## Troubleshooting

### Common Issues

1. **Connection Refused**
   - Check server is running on port 5010
   - Verify firewall settings
   - Ensure correct WebSocket URL

2. **Audio Not Working**
   - Check microphone permissions
   - Verify audio format compatibility
   - Test audio devices

3. **User Not Found Errors**
   - Ensure user registration before other operations
   - Use consistent session IDs
   - Check user data format

4. **Connection Drops**
   - Implement ping/pong heartbeat
   - Handle network interruptions
   - Add automatic reconnection

### Debug Tips
- Enable server logging for detailed information
- Monitor WebSocket connection state
- Test with simple text messages first
- Use browser developer tools for debugging

## Security Considerations

- Use secure WebSocket connections (WSS) in production
- Implement authentication and authorization
- Validate all incoming message formats
- Sanitize user inputs
- Implement rate limiting for audio streams
- Secure audio file storage and transmission

## Integration with Frontend Frameworks

### React Hook Example

```javascript
import { useState, useEffect, useRef } from 'react';

const useVoiceCall = (serverUrl = 'ws://localhost:5010') => {
  const [isConnected, setIsConnected] = useState(false);
  const [isCallActive, setIsCallActive] = useState(false);
  const [aiResponse, setAiResponse] = useState('');
  const clientRef = useRef(null);

  useEffect(() => {
    clientRef.current = new VoiceCallClient(serverUrl);
    
    const connect = async () => {
      try {
        await clientRef.current.connect();
        setIsConnected(true);
      } catch (error) {
        console.error('Failed to connect:', error);
      }
    };

    connect();

    return () => {
      if (clientRef.current) {
        clientRef.current.disconnect();
      }
    };
  }, [serverUrl]);

  const startCall = async (userData) => {
    if (clientRef.current && isConnected) {
      clientRef.current.registerUser(userData);
      await clientRef.current.startVoiceCall();
      setIsCallActive(true);
    }
  };

  const endCall = () => {
    if (clientRef.current) {
      clientRef.current.endCall();
      setIsCallActive(false);
    }
  };

  return {
    isConnected,
    isCallActive,
    aiResponse,
    startCall,
    endCall
  };
};
```

This documentation provides complete guidance for implementing voice call connections to the AI agent application. The examples demonstrate both JavaScript and Python implementations with proper error handling and best practices.