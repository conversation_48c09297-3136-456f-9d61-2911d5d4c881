# websocket_server.py

import asyncio
import json
from flask import Flask, jsonify
from flask_socketio import <PERSON><PERSON><PERSON>
from typing import Optional, Type

import websockets
from websockets.exceptions import ConnectionClosedError
from tutor.executors.user import User
from tutor.modules.logger import custom_handler, logger
# from tutor.modules.englishbot.user import User


# Initialize logger
handler = custom_handler()
logger.info("Switching to %s", handler.baseFilename)
logger.addHandler(hdlr=handler)


# List to store active users
active_users = []

def find_user_by_session(session: str) -> Optional[User]:
    """Find a user by their session ID."""
    for user in active_users:
        if user.session == session:
            return user
    return None


async def handle_message(websocket, data: dict):
    """Handle incoming WebSocket messages based on their type."""
    try:
        user = find_user_by_session(data["session"])

        if data["type"] == "audio_chunk":
            if user:
                # Convert to bytes
                audio_bytes = bytes([x & 0xFF for x in data["data"]])
                user.audio_processor.audio_queue.append(audio_bytes)
            else:
                logger.warning(f"User with session {data['session']} not found for audio_chunk")

        elif data["type"] == "store_user":
            if user:
                # Remove the user from active_users before re-adding
                try:
                    active_users.remove(user)
                except ValueError:
                    pass  # User may have already been removed, handle this gracefully

                new_user = User(
                    data["session"],
                    data["data"]["name"],
                    websocket,
                    data["data"],
                    event_loop=asyncio.get_event_loop(),
                    active_users=active_users
                )
                active_users.append(new_user)
                # user.conn = websocket
                # await user.conn.send(json.dumps({"type": "store_user", "data": "user already exists"}))
                await websocket.send(json.dumps({"type": "store_user", "data": "user update successfully"}))
            else:
                new_user = User(
                    data["session"],
                    data["data"]["name"],
                    websocket,
                    data["data"],
                    event_loop=asyncio.get_event_loop(),
                    active_users=active_users
                )
                active_users.append(new_user)
                await websocket.send(json.dumps({"type": "store_user", "data": "user added successfully"}))

        elif data["type"] == "text_input":
            if user:
               await user.process_text(data["data"])
            else:
                logger.warning(f"User with session {data['session']} not found for process_text")

        elif data["type"] == "voice_action_stop":
            if user:
               await user.process_voice(data["data"])
            else:
                logger.warning(f"User with session {data['session']} not found for process_voice")        

        elif data["type"] == "start_ai_call":
            if user:
                user.start_ai_call()
            else:
                logger.warning(f"User with session {data['session']} not found for start_ai_call")

        elif data["type"] == "ai_start_listening":
            if user:
                user.ai_start_listening = True
            else:
                logger.warning(f"User with session {data['session']} not found for ai_start_listening")

        elif data["type"] == "end_ai_call":
            if user:
                try:
                    user.end_ai_call()
                    logger.info(f"user name: {user.name} WebSocket connection closed")
                    active_users.remove(user)
                except ValueError as e:
                    logger.error(f"Error removing user: {e}")
            else:
                logger.warning(f"User with session {data['session']} not found for end_ai_call")

        elif data["type"] in ["start_accumulate", "resume_accumulate"]:
            logger.info(f"{data['type']} received")

        elif data["type"] == "request_active_users":           
            await websocket.send(json.dumps({"type": "active_users", "data": [user.to_dict() for user in active_users]}))
           
        else:
            logger.warning(f"Unknown message type received: {data['type']}")

    except json.JSONDecodeError as e:
        logger.error(f"JSON decode error: {e}")
    except KeyError as e:
        logger.error(f"Missing key in data: {e}")
    except Exception as e:
        logger.error(f"Error handling message: {e}")


async def on_request(websocket, path):
    """Handle incoming WebSocket requests."""
    session = None
    try:
        async for message in websocket:
            try:
                data = json.loads(message)
                if data["type"] != "audio_chunk":
                    logger.debug(data)
                await handle_message(websocket, data)
            except json.JSONDecodeError as e:
                logger.error(f"JSON decode error: {e}")
                await websocket.send(json.dumps({"type": "error", "message": "Invalid JSON format"}))
            except KeyError as e:
                logger.error(f"Missing key in data: {e}")
                await websocket.send(json.dumps({"type": "error", "message": "Missing key in data"}))
            except Exception as e:
                logger.error(f"Unexpected error: {e}")
                await websocket.send(json.dumps({"type": "error", "message": "Unexpected error occurred"}))
    except ConnectionClosedError as e:
        logger.warning(f"Connection closed: {e}")       
    except Exception as e:
        logger.error(f"Error in WebSocket connection: {e}")


def websocket_server() -> None:
    # Start WebSocket server
    start_server = websockets.serve(on_request, "0.0.0.0", 5010)

    # Run the server
    print("Starting WebSocket server with asyncio event loop")
    loop = asyncio.get_event_loop()
    loop.run_until_complete(start_server)
    loop.run_forever()
